"""
Redis connection manager for handling multiple Redis databases with proper connection pooling
"""

import logging
from contextlib import contextmanager
from typing import Dict, Optional, Generator
from redis import Redis, ConnectionPool
from redis.exceptions import ConnectionError, TimeoutError

logger = logging.getLogger(__name__)


# Database constants for clarity
class RedisDB:
    """Constants for Redis database numbers"""
    GREYLIST = 1     # Greylist domains (1-hour blocks)
    DANGER = 2       # Danger list domains (24-hour blocks)
    BLACKLIST = 3    # Blacklist domains (4-day blocks)


# Expiry constants
class RedisExpiry:
    """Constants for Redis key expiry times in seconds"""
    GREYLIST = 3600        # 1 hour
    DANGER = 86400         # 24 hours
    BLACKLIST = 345600     # 4 days (96 hours)


class RedisManager:
    """
    Redis connection manager that handles multiple databases with connection pooling.

    Database allocation:
    - DB 1: Greylist (1-hour blocks)
    - DB 2: Danger list (24-hour blocks)
    - DB 3: Blacklist (4-day blocks)
    """

    def __init__(self, redis_host: str, redis_port: int, socket_connect_timeout: int = 120):
        self.redis_host = redis_host
        self.redis_port = redis_port
        self.socket_connect_timeout = socket_connect_timeout
        self._connection_pools: Dict[int, ConnectionPool] = {}
        self._redis_instances: Dict[int, Redis] = {}

        # Initialize connection pools for all databases we'll use
        self._initialize_pools()

    def _initialize_pools(self) -> None:
        """Initialize connection pools for all required databases"""
        databases = [1, 2, 3]  # greylist, danger and blacklist

        for db in databases:
            try:
                connection_url = f"redis://{self.redis_host}:{self.redis_port}/{db}"
                pool = ConnectionPool.from_url(
                    connection_url,
                    socket_connect_timeout=self.socket_connect_timeout
                )
                self._connection_pools[db] = pool
                self._redis_instances[db] = Redis(connection_pool=pool)
                logger.debug(f"Initialized Redis connection pool for database {db}")
            except Exception as e:
                logger.error(f"Failed to initialize Redis pool for database {db}: {e}")
                raise

    def get_redis_instance(self, db: int) -> Redis:
        """
        Get a Redis instance for the specified database.
        :params db: Database number
        :return: Redis instance for the specified database
        """
        if db not in self._redis_instances:
            raise ValueError(f"Database {db} is not supported. Supported databases: {list(self._redis_instances.keys())}")

        try:
            # Test the connection
            instance = self._redis_instances[db]
            instance.ping()
            return instance
        except (ConnectionError, TimeoutError) as e:
            logger.error(f"Failed to connect to Redis database {db}: {e}")
            raise ConnectionError(f"Unable to connect to Redis database {db}") from e

    def set_with_expiry(self, db: int, key: str, value: str, expiry_seconds: int) -> bool:
        """
        Set a key-value pair with expiry in the specified database.
        :params db: Database number
        :params key: Redis key
        :params value: Redis value
        :params expiry_seconds: Expiry time in seconds
        :return: True if successful, False otherwise
        """
        try:
            redis_instance = self.get_redis_instance(db)
            redis_instance.set(key, value)
            redis_instance.expire(key, expiry_seconds)
            return True
        except Exception as e:
            logger.error(f"Failed to set key {key} in database {db}: {e}")
            return False

    def delete_key(self, db: int, key: str) -> bool:
        """
        Delete a key from the specified database.
        :params db: Database number
        :params key: Redis key
        :return: True if successful, False otherwise
        """
        try:
            redis_instance = self.get_redis_instance(db)
            result = redis_instance.delete(key)
            return result > 0
        except Exception as e:
            logger.error(f"Failed to delete key {key} from database {db}: {e}")
            return False

    def key_exists(self, db: int, key: str) -> bool:
        """
        Check if a key exists in the specified database.
        :params db: Database number
        :params key: Redis key
        :return: True if key exists, False otherwise
        """
        try:
            redis_instance = self.get_redis_instance(db)
            return redis_instance.exists(key) > 0
        except Exception as e:
            logger.error(f"Failed to check existence of key {key} in database {db}: {e}")
            return False
