services:
  # FastAPI python webserver
  fastapi:
    image: registry.gitlab.com/aminmemon/emailfindandverify:v1.0.0
    container_name: fastapi
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.fastapi.rule=Host(`mailer.draftss.com`)"
      - "traefik.http.routers.fastapi.entrypoints=websecure"
      - "traefik.http.routers.fastapi.tls.certresolver=emailfindverify-certresolver"
      - "traefik.http.services.fastapi.loadbalancer.server.port=8000"
    expose:
      - 8000
    environment:
      - FINDVERIFY_VERIFIER_EMAILS=<EMAIL>,<EMAIL>,<EMAIL>
      - FINDVERIFY_REDIS_HOST=redis
      - FINDVERIFY_REDIS_PORT=6379
      - FINDVERIFY_MONGO_HOST=mongodb
      - FINDVERIFY_MONGO_PORT=27017
      - AWS_ACCESS_KEY_ID=  # AWS access key id for sending emails
      - AWS_SECRET_ACCESS_KEY=  # AWS secret access key for sending emails
    working_dir: /app
    command: ["python", "-m", "uvicorn", "--host", "0.0.0.0", "main:app", "--reload"]
    depends_on:
      - redis
      - mongodb
    networks:
      - findverify-network

  celery-worker:
    image: registry.gitlab.com/aminmemon/emailfindandverify:v1.0.0
    container_name: celery
    environment:
      - FINDVERIFY_VERIFIER_EMAILS=<EMAIL>,<EMAIL>,<EMAIL>
      - FINDVERIFY_REDIS_HOST=redis
      - FINDVERIFY_REDIS_PORT=6379
      - FINDVERIFY_MONGO_HOST=mongodb
      - FINDVERIFY_MONGO_PORT=27017
    working_dir: /app
    command: ["celery", "-A", "worker.celery", "worker", "--loglevel=info"]
    depends_on:
      - redis
      - mongodb
    networks:
      - findverify-network

  # Redis for Greylist
  redis:
    image: redis:latest
    container_name: redis
    networks:
      - findverify-network

  # MongoDB to store results.
  mongodb:
    image: mongo:8.0-rc
    container_name: mongodb
    volumes:
      - $HOME/mongodb_data:/data/db
    ports:
      - 4080:27017
    networks:
      - findverify-network

  mailserver:
    image: ghcr.io/docker-mailserver/docker-mailserver:latest
    container_name: mailserver
    # Provide the FQDN of your mail server here (Your DNS MX record should point to this value)
    hostname: mail.mailer.draftss.com
    network_mode: host
    environment:
      LOG_LEVEL: debug
      PERMIT_DOCKER: host
      SSL_TYPE: letsencrypt
      SSL_DOMAIN: mail.mailer.draftss.com
      POSTMASTER_ADDRESS: <EMAIL>
      ENABLE_OPENDKIM: 1
      ENABLE_OPENDMARC: 1
      ENABLE_CLAMAV: 0
      ENABLE_SPAMASSASSIN: 0
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./mailserver/dms/mail-data/:/var/mail/
      - ./mailserver/dms/mail-state/:/var/mail-state/
      - ./mailserver/dms/mail-logs/:/var/log/mail/
      - ./mailserver/dms/config/:/tmp/docker-mailserver/
      - ./traefik/letsencrypt/acme.json:/etc/letsencrypt/acme.json:ro

  whoami:
    image: docker.io/traefik/whoami:latest
    container_name: whoami
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.whoami.rule=Host(`mail.mailer.draftss.com`)"
      - "traefik.http.routers.whoami.tls.certresolver=emailfindverify-certresolver"
      - "traefik.http.routers.whoami.entrypoints=websecure"
      - "traefik.http.services.whoami.loadbalancer.server.port=2001"
    ports:
      - 2001:80
    networks:
      - findverify-network

  traefik:
    image: "traefik:v3.0"
    container_name: traefik
    command:
      - "--log.level=DEBUG"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      # ------- Defining all required entrypoints -------
      - "--entryPoints.web.address=:80"
      - "--entryPoints.websecure.address=:443"
      # ------- For http -> https redirection -------
      - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
      - "--entrypoints.web.http.redirections.entrypoint.permanent=true"
      # ------- Defining certificate resolvers -------
      - "--certificatesresolvers.emailfindverify-certresolver.acme.email=<EMAIL>"
      - "--certificatesresolvers.emailfindverify-certresolver.acme.storage=acme.json"
      # Uncomment below line and comment out the production one for testing new setups.
      # Otherwise repeated errors might cause us to hit letsencrypt rate limit.
      # - "--certificatesresolvers.emailfindverify-certresolver.acme.caServer=https://acme-staging-v02.api.letsencrypt.org/directory"
      - "--certificatesresolvers.emailfindverify-certresolver.acme.caServer=https://acme-v02.api.letsencrypt.org/directory"
      # ------- For the acme challenge -------
      - "--certificatesresolvers.emailfindverify-certresolver.acme.httpchallenge=true"
      - "--certificatesresolvers.emailfindverify-certresolver.acme.httpchallenge.entrypoint=web"
    ports:
      - 80:80
      - 443:443
      - 8080:8080
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
      - ./traefik/logs/:/logs/traefik/
      - ./traefik/letsencrypt/acme.json:/acme.json
    logging:
      driver: "json-file"
      options:
        max-size: "1000k"
        max-file: "10"
    networks:
      - findverify-network

# Network for inter-container communication.
networks:
  findverify-network:
    driver: bridge
