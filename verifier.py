import binascii
import os
import random
import re
import socket
import time
from socket import timeout
import logging

import smtplib
from unidecode import unidecode
from MailChecker import <PERSON><PERSON><PERSON><PERSON>
from collections import named<PERSON>ple
from smtplib import SMTP
from typing import Dict, List, Optional, Tuple, Literal

from dns import resolver
from dns.exception import Timeout as DNSTimeout

from utils import send_email_notification
from redis_manager import RedisManager, RedisDB, RedisExpiry

logger = logging.getLogger("mail_verifier_logger")
logger.setLevel(logging.ERROR)


class SMTPRecipientException(Exception):  # don't cover
    def __init__(self, code, response):
        self.code = code
        self.response = response


socket.setdefaulttimeout(30)


# Some of the RCPT error status codes returned by SMTP servers
other_errors = {
    551: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 551 - Mailbox does not exists on this domain"),
    552: lambda _: dict(deliverable=True,
                        host_exists=True,
                        full_inbox=True,
                        message="Status: 552 - Their mailbox does not have enough storage currently"),
    553: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 553 - Mailbox with given name does not exists on this domain"),
    450: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 450 - Their mailbox is locked or not routable"),
    451: lambda _: dict(deliverable=False,
                        message="Status: 451 - Mail server of provided domain is currently facing some issues"),
    452: lambda _: dict(deliverable=False,
                        full_inbox=True,
                        message="Status: 452 - Message is deferred until storage opens up"),
    521: lambda _: dict(deliverable=False,
                        host_exists=False,
                        message="Status: 521 - This domain does not accept emails"),
    421: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 421 - Email service not available, try again later."),
    550: lambda _: dict(deliverable=False,
                        host_exists=True,
                        message="Status: 550 - Mailbox does not exists."),
}


def handle_unrecognized_error(_error):
    return dict(deliverable=False, message="You can't send messages to this Email ID")


def handle_connection_error(_lookup: Dict):
    """
    returns lookup data for socket connection closed events
    :param _lookup:
    :return:
    """
    _lookup['deliverable'] = False
    _lookup['catch_all'] = False
    _lookup['message'] = "You can't send messages to this Email ID"
    _lookup['debug_message'] = "Connection closed by target server"

    return _lookup


def verify_email(email_id: str,
                 from_emails_list: List[str],
                 redis_manager: RedisManager,
                 task_from: Literal["bulk", "single"] = "single") -> Tuple[str, Dict | None]:
    """
    Verifies given email and returns a Tuple of email + result dictionary. In case of bad email, returns None in place
    of result dictionary.

    :param email_id: Email address (ex. <EMAIL>)
    :param from_emails_list: List of email addresses to use as FROM address during verification.
    :param redis_manager: RedisManager instance for handling multiple Redis databases.
    :param task_from: "bulk" or "single"
    """
    try:
        # Remove unicode
        email_id = unidecode(email_id).lstrip().rstrip()

        # Check if it is in valid format
        if not MailChecker.is_valid_email_format(email_id):
            return email_id, None

        # Initialize MailVerifier class and obtain the smtp_instance
        try:
            mail_verifier = MailVerifier(from_email_list=from_emails_list,
                                         redis_manager=redis_manager,)
            result = mail_verifier.verify_mail(email_id)

            # If greylist is detected for bulk verify task, retry with delay
            if result["greylist"] and task_from == "bulk":
                logger.info(f"[*] Greylist detected, retrying...")
                result = retry_greylist_domain_with_delay(result, from_emails_list, redis_manager, "verify")

            return email_id, result

        except Exception as err:
            logger.critical(f"Mail Verifier Error (Bulk Verify): {err}", exc_info=True)
            return email_id, None

    # Main exception catcher - so that entire task doesn't fail because of one error.
    except Exception as err:
        logger.critical(err, exc_info=True)
        return email_id, None


def find_email(fname: str,
               lname: str,
               domain: str,
               from_emails_list: List[str],
               redis_manager: RedisManager,
               task_from: Literal["bulk", "single"] = "single") -> Tuple[List[str], Dict | None]:
    """
    Finds email from given first name, last name and domain. Returns a Tuple of [fn, ln, domain] + result dictionary.
    In case of bad details, returns None in place of user details list.

    :param fname: First name (ex. John)
    :param lname: Last name (ex. Doe)
    :param domain: Domain (ex. example.com)
    :param from_emails_list: List of email addresses to use as FROM address during verification.
    :param redis_manager: RedisManager instance for handling multiple Redis databases.
    :param task_from: "bulk" or "single"
    """
    # Convert domain to required format.
    domain: str = re.sub(r"^(www.|https?://www.|https?://)", "", domain, re.IGNORECASE).rstrip('/')

    # Check if all values are available.
    # We need both or one of firstname & lastname, and domain, to find emails.
    if not ((fname or lname) and domain):
        return [fname, lname, domain], None

    # Run find email.
    try:
        mail_verifier = MailVerifier(from_email_list=from_emails_list,
                                     redis_manager=redis_manager,)
        result = mail_verifier.find_mail(fname, lname, domain)
    except Exception as err:
        logger.critical(err, exc_info=True)
        return [fname, lname, domain], None

    # If greylist is detected for bulk find task, retry with delay
    if result["greylist"] and task_from == "bulk":
        logger.info(f"[*] Greylist detected, retrying...")
        result = retry_greylist_domain_with_delay(result, from_emails_list, redis_manager, "find")

    return [fname, lname, domain], result


def retry_greylist_domain_with_delay(result: Dict,
                                     verifier_emails: List[str],
                                     redis_manager: RedisManager,
                                     task_name: str = "verify") -> Dict:
    """
    Retry greylist domain with delay
    :param result: Verification result dictionary
    :param verifier_emails: List of email ids to use as FROM address during verification.
    :param redis_manager: RedisManager instance for handling multiple Redis databases.
    :param task_name: Name of the task (verify or find)
    :return: Verification result dictionary
    """
    delays = [600, 900, 1800]  # 10, 15 and 30 minutes
    for delay in delays:
        time.sleep(delay)

        if task_name == "verify":
            _, result = verify_email(result["address"][0],
                                     verifier_emails,
                                     redis_manager)

        elif task_name == "find":
            _, result = find_email(result["user_details"][0],
                                   result["user_details"][1],
                                   result["user_details"][2],
                                   verifier_emails,
                                   redis_manager)

        if not result["greylist"]:
            logger.info(f"[*] Greylist resolved, retry successful!")
            # Delete the greylist record from Redis
            domain = result["address"][1] if result.get("address") else result["user_details"][2]
            redis_manager.delete_key(RedisDB.GREYLIST, domain)
            return result

    logger.info(f"[*] Greylist could not be resolved, retry failed!")
    return result


# For storing email address data
Address = namedtuple('Address', 'email, domain')


class MailVerifier:
    def __init__(self,
                 from_email_list: List[str],
                 redis_manager: RedisManager):
        self.redis_manager = redis_manager
        self.mx_record = ""

        self.greylist_strings = ["greylisted",
                                 "try again",
                                 "later",
                                 "policy rejection",
                                 "rejected",
                                 "reject",
                                 "policy",
                                 "temporary",
                                 "temporarily",
                                 "refused",
                                 "busy",
                                 "greylisting",
                                 "resend",
                                 "shortly",
                                 "rejection",
                                 "hour",
                                 "hours",
                                 "minute",
                                 "minutes",
                                 "5.7.1",
                                 "access denied",
                                 "5.7.703",
                                 "5.7.708",
                                 "5.7.750",
                                 "IB607",
                                 "IB510",
                                 "IB110",
                                 "IB605",
                                 "IB705",
                                 "IB212",
                                 "4.2.2"]

        # Danger list strings - if found, block domain for 24 hours
        self.danger_strings = ["5.7.705", "IB113", "IB111", "IB007"]

        # Blacklist strings - if found, block domain for 4 days
        self.blacklist_strings = ["5.1.8", "spamhaus", "blacklist", "spam", "spoofing"]

        # other codes (allowed for now) -> 420, 421, 431, 441, 442, 446, 447, 450, 452, 454, 455, 471,
        self.greylist_codes = [503, 521, 523, 535, 541, 553, 554, 557, 451]

        self.email_pool = from_email_list

        # success response codes that we are assuming for various mail servers
        self.rcpt_success_codes = {
            'google': [250, 450],
            'outlook': [250],
            'zoho': [250],
            'others': [250]
        }

        # Google/Gmail MX record patterns for ESP detection
        self.google_mx_patterns = [
            'gmail-smtp-in.l.google.com',
            'aspmx.l.google.com',
            'alt1.aspmx.l.google.com',
            'alt2.aspmx.l.google.com',
            'alt3.aspmx.l.google.com',
            'alt4.aspmx.l.google.com',
            'aspmx2.googlemail.com',
            'aspmx3.googlemail.com',
            'aspmx4.googlemail.com',
            'aspmx5.googlemail.com'
        ]

        # Outlook/Microsoft MX record patterns for ESP detection
        self.outlook_mx_patterns = [
            'outlook.com',
            'hotmail.com',
            'live.com',
            'mail.protection.outlook.com',
            'ppe-hosted.com'
        ]

        # Zoho MX record patterns for ESP detection
        self.zoho_mx_patterns = [
            'mx.zoho.com',
            'mx2.zoho.com',
            'mx3.zoho.com'
        ]

        self.SOCKET_RESP_BUFFER_SIZE = 1024

    def _check_domain_blocks(self, domain: str) -> Dict[str, bool]:
        """
        Check if domain is blocked in any of the Redis databases.
        :param domain: Domain to check
        :return: Dictionary with block status for each type
        """
        blocks = {
            'greylist': False,
            'danger': False,
            'blacklist': False
        }

        blocks['greylist'] = self.redis_manager.key_exists(RedisDB.GREYLIST, domain)
        blocks['danger'] = self.redis_manager.key_exists(RedisDB.DANGER, domain)
        blocks['blacklist'] = self.redis_manager.key_exists(RedisDB.BLACKLIST, domain)

        return blocks

    def _check_danger_strings(self, response: str) -> bool:
        """
        Check if response contains any danger list strings
        :param response: SMTP response string
        :return: True if danger string found, False otherwise
        """
        response_lower = response.lower()
        return any(danger_string.lower() in response_lower for danger_string in self.danger_strings)

    def _check_blacklist_strings(self, response: str) -> bool:
        """
        Check if response contains any blacklist strings
        :param response: SMTP response string
        :return: True if blacklist string found, False otherwise
        """
        response_lower = response.lower()
        return any(blacklist_string.lower() in response_lower for blacklist_string in self.blacklist_strings)

    def _check_rcpt_response_codes(self, response: str) -> Dict[str, bool]:
        """
        Check RCPT response for specific codes that affect deliverability.
        :param response: RCPT response message
        :return: Dictionary with flags for different conditions
        """
        response_lower = response.lower()
        return {
            'full_inbox_522': '5.2.2' in response_lower,  # Full inbox
            'not_deliverable_521': '5.2.1' in response_lower  # Not deliverable
        }

    def _block_domain(self,
                      domain: str,
                      block_type: Literal['greylist', 'danger', 'blacklist'],
                      response: str = "") -> None:
        """
        Block a domain in the appropriate Redis database.
        :param domain: Domain to block
        :param block_type: Type of block ('greylist', 'danger', 'blacklist')
        :param response: Response that triggered the block (for logging)
        """
        if block_type == 'greylist':
            self.redis_manager.set_with_expiry(RedisDB.GREYLIST, domain, domain, RedisExpiry.GREYLIST)
            print(f"greylist code caught for domain {domain}")
        elif block_type == 'danger':
            self.redis_manager.set_with_expiry(RedisDB.DANGER, domain, domain, RedisExpiry.DANGER)
            print(f"danger string caught for domain {domain}: {response}")
        else:
            self.redis_manager.set_with_expiry(RedisDB.BLACKLIST, domain, domain, RedisExpiry.BLACKLIST)
            print(f"blacklist string caught for domain {domain}: {response}")
            # Send email notification
            send_email_notification(["<EMAIL>", "<EMAIL>", "<EMAIL>"],
                                    f"Blacklist detected for {domain}",
                                    f"Response: {response}")

    def _detect_esp(self, mx_record: str) -> str:
        """
        Detects the Email Service Provider based on MX record
        :param mx_record: MX record string (e.g., 'aspmx.l.google.com.')
        :return: ESP name ('google', 'outlook', 'others')
        """
        mx_record_lower = mx_record.lower()

        # Check for Google/Gmail patterns
        for pattern in self.google_mx_patterns:
            if pattern in mx_record_lower:
                return 'google'

        for pattern in self.outlook_mx_patterns:
            if pattern in mx_record_lower:
                return 'outlook'

        for pattern in self.zoho_mx_patterns:
            if pattern in mx_record_lower:
                return 'zoho'

        return 'others'

    def _calculate_will_bounce(self, lookup: Dict) -> bool:
        """
        Calculate if an email will bounce based on various factors.
        :param lookup: The lookup dictionary with email verification results
        :return: True if email will bounce, False otherwise
        """
        # will_bounce = true if:
        # - deliverable is false/null OR
        # - catchall is true OR
        # - full_inbox is true OR
        # - greylist is true

        deliverable = lookup['deliverable']
        catch_all = lookup['catch_all']
        full_inbox = lookup['full_inbox']
        greylist = lookup['greylist']

        return (deliverable is False or deliverable is None or
                catch_all or full_inbox or greylist)

    @staticmethod
    def _get_smtp_status_code(response: str) -> int:
        """
        Returns the SMTP status code for given response. If no response code was found, returns 0
        :param response: response from recipient mail server
        :return: SMTP response code; 0 if no SMTP code was found
        """
        # split into lines
        response_lines = response.split("\n")
        # remove empty strings from list
        response_lines = [line for line in response_lines if line.strip()]
        try:
            last_line: str = response_lines[-1]
        except IndexError:
            logger.error(f"smtp response was empty")
            return 0
        tokens = last_line.split(" ")
        try:
            status_code = int(tokens[0])
        except ValueError:
            logger.debug(f"Could not convert status code to number ({tokens[0]})")
            return 0

        return status_code

    def _get_mailserver_mx_record(self, domain: str) -> Optional[str]:
        """
        Fetches the highest priority MX record for this domain
        :param domain: ex. draftss.com
        :return: MX record (string) if found else None
        """
        # Get the MX_RECORD
        try:
            resolver_instance = resolver.Resolver()
            resolver_instance.timeout = 30.0
            resolver_instance.lifetime = 30.0
            resolver_answer = resolver_instance.resolve(domain, "MX")
            mail_exchangers = [exchange.to_text().split() for exchange in resolver_answer]
        except (resolver.NoAnswer, resolver.NXDOMAIN, resolver.NoNameservers, DNSTimeout) as err:
            logger.debug(f"{err}")
            return None

        if mail_exchangers:
            # Get the one with the highest priority
            return self._get_max_priority_mx_record(mail_exchangers)
        else:
            logger.debug("No Mail exchangers could be retrieved")
            return None

    @staticmethod
    def _get_max_priority_mx_record(mail_exchangers: list) -> str:
        """
        Returns MX record with lovest priority number (i.e. highest priority)
        :param mail_exchangers: list of mx records
        :return:
        """
        max_priority_mx_record = (999, 'stub')
        for record in mail_exchangers:
            if int(record[0]) < max_priority_mx_record[0]:
                max_priority_mx_record = (int(record[0]), record[1])
        return max_priority_mx_record[1]

    def _get_all_mx_records(self, domain: str) -> List[str]:
        """
        Fetches all MX records for this domain
        :param domain: ex. draftss.com
        :return: List of MX records
        """
        try:
            resolver_instance = resolver.Resolver()
            resolver_instance.timeout = 30.0
            resolver_instance.lifetime = 30.0
            resolver_answer = resolver_instance.resolve(domain, "MX")
            mail_exchangers = [exchange.to_text().split() for exchange in resolver_answer]
            return [record[1] for record in mail_exchangers]
        except (resolver.NoAnswer, resolver.NXDOMAIN, resolver.NoNameservers, DNSTimeout):
            return []

    def _connect_to_server(self, mail_server: str, mail_server_port: int) -> Optional[socket.socket]:
        """
        Creates socket connection to provided mail server & port
        :param mail_server: MX record of a domain (ex. aspmx.l.google.com)
        :param mail_server_port: Mail server communication port. Usually this is port 25.
        :return: socket connection object if successfuly created (response status is 220 or 250); else None
        """
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        # connect
        try:
            s.connect((mail_server, mail_server_port))
        except (timeout, ConnectionRefusedError):
            logger.debug("socket connection timed out")
            return None
        except socket.gaierror as err:
            logger.debug(f"{err}")
            return None

        resp = s.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()

        # stop proceeding if "bnshosting" or "octopusdns" is in resp
        if "bnshosting" in resp or "octopusdns" in resp:
            s.close()
            return None

        smtp_status_code = MailVerifier._get_smtp_status_code(resp)
        if smtp_status_code in [220, 250]:
            return s
        else:
            return None

    def _send__ehlo(self, our_domain: str, socket_connection: socket.socket) -> Tuple[int, str]:
        """
        Makes EHLO command to the target mail server. Returns SMTP status code and response.
        :param our_domain: our mailserver domain
        :param socket_connection: socket connection object
        :return: tuple of (status code, full response)
        :raises: ConnectionResetError
        """
        socket_connection.send(f"EHLO {our_domain}\r\n".encode())
        resp = socket_connection.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()
        logger.debug(resp)
        return self._get_smtp_status_code(resp), resp

    def _send__mail_from(self, our_email_id: str, socket_connection: socket.socket) -> Tuple[int, str]:
        """
        Makes MAIL FROM command to the target mail server. Returns SMTP status code and response.
        :param our_email_id: email id we are using for communication with target mail server.
        This will be used in MAIL FROM command
        :param socket_connection: socket connection object
        :return: tuple of (status code, full response)
        :raises: ConnectionResetError
        """
        socket_connection.send(f"MAIL FROM:<{our_email_id}>\r\n".encode())
        resp = socket_connection.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()
        logger.debug(resp)
        return self._get_smtp_status_code(resp), resp

    def _send__rcpt_to(self, recipient_email_id: str, socket_connection: socket.socket) -> Tuple[int, str]:
        """
        Makes RCPT TO command to the target mail server. Returns SMTP status code and response.
        :param recipient_email_id: email id to verify. This is used in RCPT TO command.
        :param socket_connection: socket connection object
        :return: tuple of (status code, full response)
        :raises: ConnectionResetError
        """
        socket_connection.send(f"RCPT TO:<{recipient_email_id}>\r\n".encode())
        resp = socket_connection.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()
        logger.debug(resp)
        return self._get_smtp_status_code(resp), resp

    def _send__quit_command(self, socket_connection: socket.socket) -> int:
        """
        Makes QUIT command to the target mail server. Returns SMTP status code. This will close the communications
        but not the actual socket connection itself. That needs to be closed manually or using with...as block
        :param socket_connection: socket connection object
        :return: status code
        :raises: ConnectionResetError
        """
        socket_connection.send(f"QUIT\r\n".encode())
        resp = socket_connection.recv(self.SOCKET_RESP_BUFFER_SIZE)
        resp = resp.decode()
        logger.debug(resp)
        return self._get_smtp_status_code(resp)

    def verify_mail(self, email: str):
        """
        Verifies the provided Email ID. Returns lookup dictionary data.
        :param email: The Email ID to verify
        :type email: str
        """

        our_email_id: str = random.choice(self.email_pool)
        logger.debug(f"using {our_email_id}")
        our_domain: str = our_email_id.split("@")[-1]

        recipient_email_id = email.lstrip().rstrip()
        recipient_domain = email.split("@")[-1]

        lookup = {
            'address': Address(email, recipient_domain),
            'valid_format': False,  # Email syntax is correct
            'deliverable': False,  # We can send messages to this email id
            'full_inbox': False,  # Their inbox is full
            'host_exists': True,  # The domain (ex. draftss.com) is a valid and existing domain
            'catch_all': False,  # Their mail servers use catch all
            'disposable': False,  # This is a disposable email id provided by such services
            'connection_error': False,  # Unable to connect to their SMTP server
            'greylist': False,  # This email / domain triggered a greylist code / string
            'dangerous': False,  # This mail server might get us blacklisted / greylisted or cause some other issues
            'will_bounce': True,  # Will this email bounce (calculated field)
            'message': "You can't send messages to this Email ID",  # Message for the user
            'debug_message': "Nothing to report",  # Message for us
            'mx_records': [],  # MX records for the domain
            'esp': 'unknown',  # Email Service Provider
            'final_smtp_code': None,  # Final SMTP response code
            'final_smtp_message': '',  # Final SMTP response message
            'smtp_handshake_log': []  # Full SMTP handshake log
        }

        # ************************************************************************************
        # =============================== DECONTAMINATION ZONE ===============================
        # ************************************************************************************

        # Check if this domain has been marked in any Redis database
        domain_blocks = self._check_domain_blocks(recipient_domain)

        if domain_blocks['danger']:
            print(f"{recipient_domain} is marked in redis danger list")
            lookup['dangerous'] = True
            lookup['deliverable'] = None
            lookup['catch_all'] = None
            lookup['connection_error'] = True
            lookup['message'] = "Couldn't perform requested operation. Domain flagged as dangerous."
            lookup['debug_message'] = f"{recipient_domain} is marked in Redis danger list."
            return lookup

        if domain_blocks['blacklist']:
            print(f"{recipient_domain} is marked in redis blacklist")
            lookup['dangerous'] = True
            lookup['deliverable'] = None
            lookup['catch_all'] = None
            lookup['connection_error'] = True
            lookup['message'] = "Couldn't perform requested operation. Domain blacklisted."
            lookup['debug_message'] = f"{recipient_domain} is marked in Redis blacklist."
            return lookup

        if domain_blocks['greylist']:
            print(f"{recipient_domain} is marked in redis greylist")
            lookup['dangerous'] = True
            lookup['message'] = "Couldn't perform requested operation. Server busy."
            lookup['debug_message'] = f"{recipient_domain} is marked in Redis for greylisting."
            return lookup

        # Validate email
        if not self._email_format_is_valid(email):
            lookup['message'] = "Not a valid Email ID format"
            lookup['message'] = f"{email} is not a valid Email ID format"
            return lookup
        else:
            lookup['valid_format'] = True

        # ************************************************************************************
        # ====================================================================================
        # ************************************************************************************

        try:
            # Get MX record and all MX records for enhanced data
            mx_record = self._get_mailserver_mx_record(recipient_domain)
            all_mx_records = self._get_all_mx_records(recipient_domain)
            lookup['mx_records'] = all_mx_records

            if not mx_record:
                lookup.update({
                    'valid_format': True,
                    'deliverable': False,
                    'full_inbox': False,
                    'host_exists': False,
                    'catch_all': False,
                    'disposable': False,
                    'connection_error': True,
                    'greylist': False,
                    'dangerous': False,
                    'message': "We could not find any mail server for this domain",
                    'debug_message': f"No MX record could be found for {recipient_domain}"
                })
                return lookup

            # Detect ESP
            esp = self._detect_esp(mx_record)
            lookup['esp'] = esp

            # Create connection
            s = self._connect_to_server(mx_record, 25)
            if not s:
                logger.debug(f"Could not connect to {mx_record}:{25} for {email}")
                return {
                    'address': Address(email, recipient_domain),
                    'valid_format': True,
                    'deliverable': False,
                    'full_inbox': False,
                    'host_exists': False,
                    'catch_all': False,
                    'disposable': False,
                    'connection_error': True,
                    'greylist': False,
                    'dangerous': False,
                    'message': f"We could not connect to {recipient_domain}'s mail server",
                    'debug_message': f"Could not connect to {mx_record}:{25} ({email})"
                }

            # Run EHLO command
            try:
                ehlo_status, ehlo_response = self._send__ehlo(our_domain, s)
                lookup['smtp_handshake_log'].append(f"EHLO {our_domain} -> {ehlo_status}: {ehlo_response.strip()}")
            except (ConnectionResetError, BrokenPipeError):
                return handle_connection_error(lookup)
            except Exception as err:
                logger.error(f"Error during EHLO: {err}")
                lookup['smtp_handshake_log'].append(f"EHLO {our_domain} -> ERROR: {err}")
                s.close()
                return lookup

            # Run MAIL FROM command
            try:
                mail_from_status, mail_from_response = self._send__mail_from(our_email_id, s)
                lookup['smtp_handshake_log'].append(f"MAIL FROM:<{our_email_id}> -> {mail_from_status}: {mail_from_response.strip()}")
            except (ConnectionResetError, BrokenPipeError):
                return handle_connection_error(lookup)
            except Exception as err:
                logger.error(f"Error during MAIL FROM: {err}")
                lookup['smtp_handshake_log'].append(f"MAIL FROM:<{our_email_id}> -> ERROR: {err}")
                s.close()
                return lookup

            # Run RCPT TO command
            # NOTE: 'Not Deliverable' case is automatically handled since its default value is False
            try:
                rcpt_status, rcpt_response = self._send__rcpt_to(recipient_email_id, s)
                lookup['smtp_handshake_log'].append(f"RCPT TO:<{recipient_email_id}> -> {rcpt_status}: {rcpt_response.strip()}")
                lookup['final_smtp_code'] = rcpt_status
                lookup['final_smtp_message'] = rcpt_response.strip()
            except Exception as err:
                logger.error(f"Error during RCPT TO: {err}")
                lookup['smtp_handshake_log'].append(f"RCPT TO:<{recipient_email_id}> -> ERROR: {err}")
                return lookup

            # Get ESP-specific success codes
            esp_success_codes = self.rcpt_success_codes.get(esp, self.rcpt_success_codes['others'])

            # Check for danger strings first (highest priority)
            if self._check_danger_strings(rcpt_response):
                lookup["dangerous"] = True
                lookup['deliverable'] = None
                lookup['catch_all'] = None
                lookup['connection_error'] = True
                lookup['message'] = "Couldn't perform requested operation. Domain flagged as dangerous."
                lookup['debug_message'] = f"Danger string detected in RCPT response for {recipient_domain}."
                self._block_domain(recipient_domain, 'danger', rcpt_response)

            # Check for blacklist strings (second priority)
            elif self._check_blacklist_strings(rcpt_response):
                lookup["dangerous"] = True
                lookup['deliverable'] = None
                lookup['catch_all'] = None
                lookup['connection_error'] = True
                lookup['message'] = "Couldn't perform requested operation. Domain blacklisted."
                lookup['debug_message'] = f"Blacklist string detected in RCPT response for {recipient_domain}."
                self._block_domain(recipient_domain, 'blacklist', rcpt_response)

            # Check for greylist conditions (third priority)
            elif rcpt_status in self.greylist_codes or \
                any(greylist_string in rcpt_response.lower() for greylist_string in self.greylist_strings):
                lookup["greylist"] = True
                lookup['message'] = "Couldn't perform requested operation. Server busy."
                lookup['debug_message'] = f"{recipient_domain} is marked in Redis for greylisting."
                self._block_domain(recipient_domain, 'greylist', rcpt_response)

            elif rcpt_status in esp_success_codes:
                # Check for specific RCPT response codes
                rcpt_codes = self._check_rcpt_response_codes(rcpt_response)

                if rcpt_codes['not_deliverable_521']:
                    # 5.2.1 found - mark as not deliverable
                    lookup['deliverable'] = False
                    lookup['message'] = "Email address does not exist"
                    lookup['debug_message'] = f"5.2.1 code found in RCPT response"
                elif rcpt_codes['full_inbox_522']:
                    # 5.2.2 found - mark full inbox but deliverable
                    lookup['deliverable'] = True
                    lookup['full_inbox'] = True
                    lookup['message'] = "Email address exists but inbox is full"
                    lookup['debug_message'] = f"5.2.2 code found in RCPT response"
                else:
                    # Normal success case - deliverable if gibberish rcpt check in next step doesn't return status code 250
                    lookup['deliverable'] = True
                    lookup['message'] = "You can send messages to this Email ID"
                    lookup['debug_message'] = f"first rcpt() returned status code {rcpt_status}"

                # RCPT TO - gibberish email (only if not already marked as non-deliverable)
                if lookup['deliverable']:
                    try:
                        gibberish_email_rcpt_status, gibberish_response = self._send__rcpt_to(self._generate_random_email(recipient_domain), s)
                        lookup['smtp_handshake_log'].append(f"RCPT TO:<gibberish> -> {gibberish_email_rcpt_status}: {gibberish_response.strip()}")

                        # Only consider 250 code for catch-all detection to avoid greylisting issues
                        if gibberish_email_rcpt_status == 250:
                            # Catch All case
                            lookup['catch_all'] = True
                            lookup['message'] = "This domain uses a catch-all mailbox"
                            lookup['debug_message'] = f"catch_all rcpt() returned status code {gibberish_email_rcpt_status}"
                    except Exception as err:
                        logger.error(f"Error during gibberish RCPT TO: {err}")
                        lookup['smtp_handshake_log'].append(f"RCPT TO:<gibberish> -> ERROR: {err}")
                        lookup['catch_all'] = True
                        lookup['message'] = "This domain uses a catch-all mailbox"
                        lookup['debug_message'] = f"gibberish RCPT ran into some error"

            elif rcpt_status >= 400:
                lookup['debug_message'] = f"rcpt() returned status code {rcpt_status}"
                raise SMTPRecipientException(code=rcpt_status, response="RCPT failed on first try")

            # run QUIT command and close the connection
            try:
                self._send__quit_command(s)
            except (ConnectionResetError, BrokenPipeError):
                # connection was already closed. don't do anything in this case
                pass
            except Exception as err:
                logger.error(f"Error during QUIT command: {err}")

            s.close()

        except SMTPRecipientException as err:
            kwargs = other_errors.get(err.code, handle_unrecognized_error)(err.response)
            # Merge with lookup dictionary
            lookup = {**lookup, **kwargs}
            if 's' in locals():
                # run QUIT command and close connection
                try:
                    # noinspection PyUnboundLocalVariable
                    self._send__quit_command(s)
                except (ConnectionResetError, BrokenPipeError):
                    return handle_connection_error(lookup)
                except Exception as err:
                    logger.error(f"Error during QUIT command: {err}")
                s.close()

        except smtplib.SMTPServerDisconnected as err:
            # Might be blocked by Spamhaus or other such services
            lookup['message'] = "Connection closed unexpectedly by services"
            lookup['debug_message'] = f"rcpt() or mail() went into SMTPServerDisconnected exception\n" \
                                      f"Error is as follows: {err}"
            if 's' in locals():
                # run QUIT command and close connection
                try:
                    # noinspection PyUnboundLocalVariable
                    self._send__quit_command(s)
                except (ConnectionResetError, BrokenPipeError):
                    return handle_connection_error(lookup)
                except Exception as err:
                    logger.error(f"Error during QUIT command: {err}")
                s.close()

        except smtplib.SMTPConnectError as err:
            # Connection Refused. You might be blacklisted
            lookup['message'] = "Connection closed unexpectedly"
            lookup['debug_message'] = f"rcpt() or mail() went into SMTPConnectError\n" \
                                      f"Error is as follows {err}"
            if 's' in locals():
                # run QUIT command and close connection
                try:
                    # noinspection PyUnboundLocalVariable
                    self._send__quit_command(s)
                except (ConnectionResetError, BrokenPipeError):
                    return handle_connection_error(lookup)
                except Exception as err:
                    logger.error(f"Error during QUIT command: {err}")
                s.close()

        # Calculate will_bounce field before returning
        lookup['will_bounce'] = self._calculate_will_bounce(lookup)
        return lookup

    def find_mail(self, firstname: str, lastname: str, recipient_domain: str) -> Dict:
        """
        Finds email id using provided user details
        :param firstname: ex. amin
        :param lastname: ex. memon
        :param recipient_domain: ex. draftss.com
        :return: result dictionary
        """

        # Default results
        lookup = {
            'address': Address(None, recipient_domain),
            'user_details': [firstname, lastname, recipient_domain],
            'valid_format': True,  # Email syntax is correct
            'deliverable': False,  # We can send messages to this email id
            'full_inbox': False,  # Their inbox is full
            'host_exists': True,  # The domain (ex. draftss.com) is a valid and existing domain
            'catch_all': False,  # Their mail servers use catch all
            'disposable': False,  # This is a disposable email id provided by such services
            'connection_error': False,  # Unable to connect to their SMTP server
            'greylist': False,  # This email / domain triggered a greylist code / string
            'dangerous': False,  # This mail server might get us blacklisted / greylisted or cause some other issues
            'will_bounce': True,  # Will this email bounce (calculated field)
            'message': f"No emails found for this person",  # Message for the user
            'debug_message': "Nothing to report",  # Message for us
            'mx_records': [],  # MX records for the domain
            'esp': 'unknown',  # Email Service Provider
            'final_smtp_code': None,  # Final SMTP response code
            'final_smtp_message': '',  # Final SMTP response message
            'smtp_handshake_log': []  # Full SMTP handshake log
        }

        our_email_id: str = random.choice(self.email_pool)
        our_domain: str = our_email_id.split("@")[-1]

        # Clean the names
        firstname = firstname.lower().lstrip().rstrip()
        lastname = lastname.lower().lstrip().rstrip()
        recipient_domain = recipient_domain.lower().lstrip().rstrip()

        # Check if this domain has been marked in any Redis database
        domain_blocks = self._check_domain_blocks(recipient_domain)

        if domain_blocks['danger']:
            print(f"{recipient_domain} is marked in redis danger list")
            lookup['dangerous'] = True
            lookup['deliverable'] = None
            lookup['catch_all'] = None
            lookup['connection_error'] = True
            lookup['message'] = "Couldn't perform requested operation. Domain flagged as dangerous."
            lookup['debug_message'] = f"{recipient_domain} is marked in Redis danger list."
            lookup['will_bounce'] = self._calculate_will_bounce(lookup)
            return lookup

        if domain_blocks['blacklist']:
            print(f"{recipient_domain} is marked in redis blacklist")
            lookup['dangerous'] = True
            lookup['deliverable'] = None
            lookup['catch_all'] = None
            lookup['connection_error'] = True
            lookup['message'] = "Couldn't perform requested operation. Domain blacklisted."
            lookup['debug_message'] = f"{recipient_domain} is marked in Redis blacklist."
            lookup['will_bounce'] = self._calculate_will_bounce(lookup)
            return lookup

        if domain_blocks['greylist']:
            print(f"{recipient_domain} is marked in redis greylist")
            lookup['dangerous'] = True
            lookup['message'] = "Couldn't perform requested operation. Server busy."
            lookup['debug_message'] = f"{recipient_domain} is marked in Redis for greylisting."
            lookup['will_bounce'] = self._calculate_will_bounce(lookup)
            return lookup

        # Get MX record and all MX records for enhanced data
        mx_record = self._get_mailserver_mx_record(recipient_domain)
        all_mx_records = self._get_all_mx_records(recipient_domain)
        lookup['mx_records'] = all_mx_records

        if not mx_record:
            lookup['debug_message'] = f"No MX record found for {recipient_domain}"
            return lookup

        # Detect ESP
        esp = self._detect_esp(mx_record)
        lookup['esp'] = esp

        # Get email combinations
        email_list = self._generate_emails_from_name(firstname, lastname, recipient_domain)

        # =============================================================================================
        # ---------------------------------------------------------------------------------------------
        # =============================================================================================

        # Create connection
        s = self._connect_to_server(mx_record, 25)
        if not s:
            lookup['debug_message'] = f"Could not connect with {mx_record}"
            return lookup

        # Run EHLO command
        try:
            ehlo_status, ehlo_response = self._send__ehlo(our_domain, s)
            lookup['smtp_handshake_log'].append(f"EHLO {our_domain} -> {ehlo_status}: {ehlo_response.strip()}")
        except (ConnectionResetError, BrokenPipeError):
            return handle_connection_error(lookup)

        # Run MAIL FROM command
        try:
            mail_from_status, mail_from_response = self._send__mail_from(our_email_id, s)
            lookup['smtp_handshake_log'].append(f"MAIL FROM:<{our_email_id}> -> {mail_from_status}: {mail_from_response.strip()}")
        except (ConnectionResetError, BrokenPipeError):
            return handle_connection_error(lookup)

        # Detect ESP for this domain
        esp = self._detect_esp(mx_record)
        esp_success_codes = self.rcpt_success_codes.get(esp, self.rcpt_success_codes['others'])

        try:
            # For each email try sending RCPT command
            for email in email_list:
                # Run RCPT TO command
                # NOTE: 'Not Deliverable' case is automatically handled since its default value is False
                rcpt_status, rcpt_response = self._send__rcpt_to(email, s)
                lookup['smtp_handshake_log'].append(f"RCPT TO:<{email}> -> {rcpt_status}: {rcpt_response.strip()}")
                lookup['final_smtp_code'] = rcpt_status
                lookup['final_smtp_message'] = rcpt_response.strip()

                # Check for danger strings first (highest priority)
                if self._check_danger_strings(rcpt_response):
                    lookup["dangerous"] = True
                    lookup['deliverable'] = None
                    lookup['catch_all'] = None
                    lookup['connection_error'] = True
                    lookup['message'] = "Couldn't perform requested operation. Domain flagged as dangerous."
                    lookup['debug_message'] = f"Danger string detected in RCPT response for {recipient_domain}."
                    self._block_domain(recipient_domain, 'danger', rcpt_response)
                    break

                # Check for blacklist strings (second priority)
                elif self._check_blacklist_strings(rcpt_response):
                    lookup["dangerous"] = True
                    lookup['deliverable'] = None
                    lookup['catch_all'] = None
                    lookup['connection_error'] = True
                    lookup['message'] = "Couldn't perform requested operation. Domain blacklisted."
                    lookup['debug_message'] = f"Blacklist string detected in RCPT response for {recipient_domain}."
                    self._block_domain(recipient_domain, 'blacklist', rcpt_response)
                    break

                # Check for greylist conditions (third priority)
                elif rcpt_status in self.greylist_codes or \
                    any(greylist_string in rcpt_response.lower() for greylist_string in self.greylist_strings):
                    lookup["greylist"] = True
                    lookup['message'] = "Couldn't perform requested operation. Server busy."
                    lookup['debug_message'] = f"{recipient_domain} is marked in Redis for greylisting."
                    self._block_domain(recipient_domain, 'greylist', rcpt_response)
                    break

                elif rcpt_status in esp_success_codes:
                    # RCPT TO - gibberish email
                    gibberish_email_rcpt_status, gibberish_response = self._send__rcpt_to(
                        self._generate_random_email(recipient_domain), s)
                    lookup['smtp_handshake_log'].append(f"RCPT TO:<gibberish> -> {gibberish_email_rcpt_status}: {gibberish_response.strip()}")

                    # Only consider 250 code for catch-all detection to avoid greylisting issues
                    if gibberish_email_rcpt_status == 250:
                        # Email is catch-all. No need to look further
                        lookup['address'] = Address(None, recipient_domain)
                        lookup['deliverable'] = True
                        lookup['catch_all'] = True
                        lookup['message'] = "This recipient_domain uses a catch-all mailbox"
                        lookup['debug_message'] = f"second rcpt check failed" \
                                                  f" with status code {gibberish_email_rcpt_status}"
                        # run QUIT command and close the connection
                        try:
                            self._send__quit_command(s)
                        except (ConnectionResetError, BrokenPipeError):
                            return handle_connection_error(lookup)
                        except Exception as err:
                            logger.error(f"Error during QUIT command: {err}")
                        s.close()
                        lookup['will_bounce'] = self._calculate_will_bounce(lookup)
                        return lookup
                    else:
                        # Email is deliverable. We found it!
                        lookup['address'] = Address(email, recipient_domain)
                        lookup['deliverable'] = True
                        lookup['message'] = "You can send messages to this Email ID"
                        # run QUIT command and close the connection
                        try:
                            self._send__quit_command(s)
                        except (ConnectionResetError, BrokenPipeError):
                            return handle_connection_error(lookup)
                        except Exception as err:
                            logger.error(f"Error during QUIT command: {err}")
                        s.close()
                        lookup['will_bounce'] = self._calculate_will_bounce(lookup)
                        return lookup

                # Reset SMTP state for next RCPT
                # self._reset_smtp_state_to_before_rcpt(smtp_instance, our_email_id)

        except Exception as err:
            logger.error(err)
            lookup['debug_message'] = f"Error occured while running RCPT: {err}"
            # run QUIT command and close the connection
            try:
                self._send__quit_command(s)
            except (ConnectionResetError, BrokenPipeError):
                return handle_connection_error(lookup)
            except Exception as err:
                logger.error(f"Error during QUIT command: {err}")
            s.close()
            lookup['will_bounce'] = self._calculate_will_bounce(lookup)
            return lookup

        # run QUIT command and close the connection
        try:
            self._send__quit_command(s)
        except (ConnectionResetError, BrokenPipeError):
            # Don't do anything
            pass
        except Exception as err:
            logger.error(f"Error during QUIT command: {err}")
        s.close()

        # Calculate will_bounce field before returning
        lookup['will_bounce'] = self._calculate_will_bounce(lookup)
        return lookup

    @staticmethod
    def _generate_emails_from_name(fn, ln, dn) -> List:
        """
        Generates email ids using firstname, lastname and domain values
        :param fn: first name
        :param ln: last name
        :param dn: domain

        :return: List of email ids
        """
        if fn and ln:
            combinations = {
                'c0': lambda _fn, _ln, _dn: f"{_fn + _ln}@{_dn}",
                'c1': lambda _fn, _ln, _dn: f"{_ln + _fn}@{_dn}",
                'c2': lambda _fn, _ln, _dn: f"{_fn[0] + _ln}@{_dn}",
                'c3': lambda _fn, _ln, _dn: f"{_ln[0] + _fn}@{_dn}",
                'c4': lambda _fn, _ln, _dn: f"{_fn}@{_dn}",
                'c5': lambda _fn, _ln, _dn: f"{_ln}@{_dn}",
                'c6': lambda _fn, _ln, _dn: f"{_fn + '.' + _ln}@{_dn}",
                'c7': lambda _fn, _ln, _dn: f"{_ln + '.' + _fn}@{_dn}",
            }
        elif not fn:
            combinations = {
                'c0': lambda _fn, _ln, _dn: f"{_ln}@{_dn}",
            }

        else:
            combinations = {
                'c0': lambda _fn, _ln, _dn: f"{_fn}@{_dn}",
            }

        email_list = [combinations[f"c{i}"](fn, ln, dn) for i in range(len(combinations.keys()))]

        return email_list

    @staticmethod
    def _reset_smtp_state_to_before_rcpt(smtp_instance: SMTP, our_email_id: str):
        """
        This function reset SMTP connection to its original state and runs HELO & MAIL FROM commands.
        Some mail servers return (452 4.5.3 Too many recipients) response. Resetting SMTP state helps to work around
        this issue.
        :param smtp_instance: SMTP object
        :return: void
        """
        # Reset connection to original state
        smtp_instance.rset()
        smtp_instance.helo(name='liverify2.prospectss.com')
        smtp_instance.mail(our_email_id)

    def _get_success_codes_list(self, mx_record_domain: str) -> List[int]:
        """
        Returns list of RCPT codes that we are assuming to be success responses
        :param mx_record_domain: Fetch this from MX record (ex. google.com)
        :return: List of success codes (int)
        """
        if mx_record_domain in self.rcpt_success_codes:
            return self.rcpt_success_codes[mx_record_domain]
        else:
            return self.rcpt_success_codes['others']

    @staticmethod
    def _generate_random_email(domain: str) -> str:
        """
        Generates gibberish email id using provided domain
        :param domain: ex. draftss.com
        :return: gibberish email
        """
        name = binascii.hexlify(os.urandom(20)).decode()[:5]
        return f"{name}@{domain}"

    @staticmethod
    def _email_format_is_valid(email_id) -> bool:
        """
        Regex based email id format validation
        :param email_id: ex. <EMAIL>
        """
        email_regex = r"(^[a-zA-Z0-9_.+-]+@[a-zA-Z0-9-]+\.[a-zA-Z0-9-.]+$)"
        if re.match(email_regex, email_id):  # re.match() returns a Match object
            return True
        else:
            return False
