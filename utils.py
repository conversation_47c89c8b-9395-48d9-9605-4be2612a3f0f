"""
Utility functions for email verification, MongoDB operations, and greylist retry logic
"""

import os
import logging
import datetime
from typing import Dict, List

import boto3
from botocore.exceptions import NoCredentialsError, ClientError
from pymongo import MongoClient

logger = logging.getLogger(__name__)


def get_mongo_connection():
    """Get MongoDB connection"""
    mongo_host = os.environ['FINDVERIFY_MONGO_HOST']
    mongo_port = int(os.environ['FINDVERIFY_MONGO_PORT'])
    return MongoClient(mongo_host, mongo_port)


def get_mongo_collections():
    """Get MongoDB collections for email verification"""
    client = get_mongo_connection()
    db = client["findverify_db"]
    return {
        'single_verify_tasks': db["single_verify_tasks"],
        'single_find_tasks': db["single_find_tasks"],
        'bulk_verify_tasks': db["bulk_verify_tasks"],
        'bulk_find_tasks': db["bulk_find_tasks"]
    }


def save_single_find_or_verify_result(result: Dict, task_name: str) -> str:
    """
    Save single find or verify result to MongoDB
    :param result: Verification result dictionary
    :param task_name: Name of the task (verify or find)
    :return: Document ID
    """
    try:
        collections = get_mongo_collections()

        document = {
            'status': "completed",
            'created_on': datetime.datetime.now(datetime.timezone.utc),
            'result': result,
        }

        result_doc = collections[f'single_{task_name}_tasks'].insert_one(document)
        return str(result_doc.inserted_id)

    except Exception as e:
        logger.error(f"Error saving single verification: {e}")
        return None


def send_email_notification(recipient_emails: List[str], subject: str, body: str):
    """
    Send email notification using AWS SES.
    :param recipient_emails: List of recipient email addresses
    :param subject: Email subject
    :param body: Email body
    """
    ses_client = boto3.client('ses', region_name='us-east-1')
    source_email = "<EMAIL>"

    try:
        response = ses_client.send_email(
            Source=source_email,
            Destination={'ToAddresses': recipient_emails},
            Message={
                'Subject': {'Data': subject},
                'Body': {'Text': {'Data': body}}
            }
        )
        logger.info(f"Email sent successfully: {response['MessageId']}")

    except NoCredentialsError:
        logger.error("AWS credentials not available")

    except ClientError as e:
        logger.error(f"Error sending email: {e}")

    except Exception as e:
        logger.error(f"Unexpected error sending email: {e}")
